from decimal import Decimal, getcontext
from hashlib import md5
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

# Set high precision for square roots
getcontext().prec = 30

leak = "4336282047950153046404"
ct_hex = "7863c63a4bb2c782eb67f32928a1deceaee0259d096b192976615fba644558b2ef62e48740f7f28da587846a81697745"
ct = bytes.fromhex(ct_hex)

for k in range(10**10, 10**11):
    root = Decimal(k).sqrt()
    parts = str(root).split('.')
    if len(parts) < 2:
        continue  # skip perfect squares

    root_str = parts[1]
    if root_str.startswith(leak):
        print(f"[+] Found K: {k}")
        key = md5(str(k).encode()).digest()
        cipher = AES.new(key, AES.MODE_ECB)
        pt = unpad(cipher.decrypt(ct), 16)
        print("Flag:", pt.decode())
        break
